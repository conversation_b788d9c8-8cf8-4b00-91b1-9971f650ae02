// Simplified Grave Cleaning Management System
// Data structures and functionality for managing grave cleaning tasks

// Global data storage
let cleaningData = {
    customers: [],
    graves: [],
    tasks: [],
    payments: [],
    currentDate: new Date()
};

// Task status definitions for cleaning system
const CLEANING_STATUS = {
    COMPLETED: 'completed',
    IN_PROGRESS: 'in_progress', 
    PENDING: 'pending',
    SKIPPED: 'skipped'
};

// Cemetery definitions
const CEMETERIES = {
    hlavny: 'Hlavný cintorín',
    petrzalka: 'Petržalský cintorín'
};

// Initialize cleaning system
document.addEventListener('DOMContentLoaded', function() {
    initializeCleaningSystem();
    loadCleaningData();
    updateCleaningDashboard();
});

function initializeCleaningSystem() {
    // Navigation handling
    const cleaningNavTabs = document.querySelectorAll('.cleaning-nav-tab');
    cleaningNavTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const viewName = this.dataset.cleaningView;
            switchCleaningView(viewName);
        });
    });

    // Load sample data if none exists
    if (cleaningData.customers.length === 0) {
        loadSampleCleaningData();
    }
}

function switchCleaningView(viewName) {
    // Hide all views
    const allViews = document.querySelectorAll('.cleaning-view');
    allViews.forEach(view => view.classList.remove('active'));

    // Remove active class from all tabs
    const allTabs = document.querySelectorAll('.cleaning-nav-tab');
    allTabs.forEach(tab => tab.classList.remove('active'));

    // Show selected view
    const targetView = document.getElementById(`cleaning-${viewName}`);
    if (targetView) {
        targetView.classList.add('active');
    }

    // Add active class to clicked tab
    const activeTab = document.querySelector(`[data-cleaning-view="${viewName}"]`);
    if (activeTab) {
        activeTab.classList.add('active');
    }

    // Update view content
    switch(viewName) {
        case 'dashboard':
            updateCleaningDashboard();
            break;
        case 'today':
            updateTodayTasks();
            break;
        case 'schedule':
            updateScheduleView();
            break;
        case 'customers':
            updateCustomersView();
            break;
        case 'graves':
            updateGravesView();
            break;
        case 'payments':
            updatePaymentsView();
            break;
    }
}

function updateCleaningDashboard() {
    const today = new Date();
    const thisMonth = today.getMonth();
    const thisYear = today.getFullYear();

    // Calculate statistics
    const activeCustomers = cleaningData.customers.filter(c => c.active).length;
    const completedThisMonth = cleaningData.tasks.filter(t => 
        t.status === CLEANING_STATUS.COMPLETED && 
        new Date(t.date).getMonth() === thisMonth
    ).length;
    
    const unpaidCustomers = cleaningData.customers.filter(c => !c.paid).length;
    const todayTasks = cleaningData.tasks.filter(t => 
        new Date(t.date).toDateString() === today.toDateString()
    ).length;
    
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowTasks = cleaningData.tasks.filter(t => 
        new Date(t.date).toDateString() === tomorrow.toDateString()
    ).length;

    const monthlyIncome = cleaningData.payments
        .filter(p => p.paid && new Date(p.date).getMonth() === thisMonth)
        .reduce((sum, p) => sum + p.amount, 0);

    // Update dashboard elements
    updateElementText('activeCustomers', activeCustomers);
    updateElementText('completedThisMonth', `${completedThisMonth}/${activeCustomers}`);
    updateElementText('monthlyIncome', `${monthlyIncome}€`);
    updateElementText('unpaidCustomers', `${unpaidCustomers} zákazníci`);
    updateElementText('todayCleanings', todayTasks);
    updateElementText('tomorrowCleanings', tomorrowTasks);

    // Update today's tasks list
    updateTodayTasksList();
}

function updateTodayTasksList() {
    const today = new Date();
    const todayTasks = cleaningData.tasks.filter(t => 
        new Date(t.date).toDateString() === today.toDateString()
    );

    const tasksListElement = document.getElementById('todayTasksList');
    if (!tasksListElement) return;

    if (todayTasks.length === 0) {
        tasksListElement.innerHTML = '<p class="text-muted">Žiadne úlohy na dnes</p>';
        return;
    }

    const tasksHTML = todayTasks.map(task => {
        const customer = cleaningData.customers.find(c => c.id === task.customerId);
        const statusIcon = getStatusIcon(task.status);
        const statusClass = getStatusClass(task.status);
        
        return `
            <div class="task-item ${statusClass}" onclick="showTaskDetail('${task.id}')">
                <div class="task-status">${statusIcon}</div>
                <div class="task-info">
                    <span class="task-grave">${task.graveNumber} (${customer ? customer.name : 'Neznámy'})</span>
                    <span class="task-time">${getStatusText(task.status)}</span>
                </div>
            </div>
        `;
    }).join('');

    tasksListElement.innerHTML = tasksHTML;
}

function getStatusIcon(status) {
    switch(status) {
        case CLEANING_STATUS.COMPLETED: return '✅';
        case CLEANING_STATUS.IN_PROGRESS: return '⏳';
        case CLEANING_STATUS.PENDING: return '📅';
        case CLEANING_STATUS.SKIPPED: return '❌';
        default: return '📅';
    }
}

function getStatusClass(status) {
    switch(status) {
        case CLEANING_STATUS.COMPLETED: return 'completed';
        case CLEANING_STATUS.IN_PROGRESS: return 'in-progress';
        case CLEANING_STATUS.PENDING: return 'pending';
        case CLEANING_STATUS.SKIPPED: return 'skipped';
        default: return 'pending';
    }
}

function getStatusText(status) {
    switch(status) {
        case CLEANING_STATUS.COMPLETED: return 'Dokončené';
        case CLEANING_STATUS.IN_PROGRESS: return 'V riešení';
        case CLEANING_STATUS.PENDING: return 'Čaká';
        case CLEANING_STATUS.SKIPPED: return 'Preskočené';
        default: return 'Čaká';
    }
}

function updateElementText(elementId, text) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = text;
    }
}

function loadSampleCleaningData() {
    // Sample customers
    cleaningData.customers = [
        {
            id: '1',
            name: 'Mária Nováková',
            phone: '0903 123 456',
            email: '<EMAIL>',
            graveNumber: 'A-123',
            cemetery: 'hlavny',
            monthlyFee: 40,
            active: true,
            paid: true,
            lastCleaning: '2024-12-15',
            nextCleaning: '2025-01-15'
        },
        {
            id: '2',
            name: 'Ján Kováč',
            phone: '0903 456 789',
            email: '<EMAIL>',
            graveNumber: 'B-456',
            cemetery: 'petrzalka',
            monthlyFee: 40,
            active: true,
            paid: false,
            lastCleaning: '2024-12-10',
            nextCleaning: '2025-01-10'
        },
        {
            id: '3',
            name: 'Peter Svoboda',
            phone: '0903 789 012',
            email: '<EMAIL>',
            graveNumber: 'C-789',
            cemetery: 'hlavny',
            monthlyFee: 40,
            active: true,
            paid: true,
            lastCleaning: '2024-12-17',
            nextCleaning: '2025-01-17'
        }
    ];

    // Sample tasks for today
    const today = new Date();
    cleaningData.tasks = [
        {
            id: 'task-1',
            customerId: '1',
            graveNumber: 'A-123',
            cemetery: 'hlavny',
            date: today.toISOString().split('T')[0],
            status: CLEANING_STATUS.COMPLETED,
            completedAt: '14:30'
        },
        {
            id: 'task-2',
            customerId: '2',
            graveNumber: 'B-456',
            cemetery: 'petrzalka',
            date: today.toISOString().split('T')[0],
            status: CLEANING_STATUS.IN_PROGRESS
        },
        {
            id: 'task-3',
            customerId: '3',
            graveNumber: 'C-789',
            cemetery: 'hlavny',
            date: today.toISOString().split('T')[0],
            status: CLEANING_STATUS.PENDING
        }
    ];

    // Sample payments
    cleaningData.payments = [
        {
            id: 'pay-1',
            customerId: '1',
            amount: 40,
            date: '2025-01-01',
            paid: true
        },
        {
            id: 'pay-2',
            customerId: '2',
            amount: 40,
            date: '2025-01-02',
            paid: false,
            overdueDays: 7
        },
        {
            id: 'pay-3',
            customerId: '3',
            amount: 40,
            date: '2025-01-03',
            paid: true
        }
    ];

    saveCleaningData();
}

function loadCleaningData() {
    const savedData = localStorage.getItem('cleaningSystemData');
    if (savedData) {
        cleaningData = JSON.parse(savedData);
    }
}

function saveCleaningData() {
    localStorage.setItem('cleaningSystemData', JSON.stringify(cleaningData));
}

function updateTodayTasks() {
    const today = new Date();
    const todayTasks = cleaningData.tasks.filter(t =>
        new Date(t.date).toDateString() === today.toDateString()
    );

    // Group tasks by cemetery
    const tasksByCemetery = {};
    todayTasks.forEach(task => {
        if (!tasksByCemetery[task.cemetery]) {
            tasksByCemetery[task.cemetery] = [];
        }
        tasksByCemetery[task.cemetery].push(task);
    });

    const container = document.querySelector('.today-tasks-container');
    if (!container) return;

    let html = '';
    Object.keys(tasksByCemetery).forEach(cemetery => {
        const cemeteryName = CEMETERIES[cemetery] || cemetery;
        const tasks = tasksByCemetery[cemetery];

        html += `
            <div class="cemetery-group">
                <h3><i class="fas fa-monument"></i> ${cemeteryName}</h3>
                <div class="cemetery-tasks">
        `;

        tasks.forEach(task => {
            const customer = cleaningData.customers.find(c => c.id === task.customerId);
            const statusIcon = getStatusIcon(task.status);
            const statusClass = getStatusClass(task.status);
            const statusText = getStatusText(task.status);

            html += `
                <div class="task-item ${statusClass}">
                    <div class="task-status">${statusIcon}</div>
                    <div class="task-info">
                        <span class="task-grave">${task.graveNumber} (${customer ? customer.name : 'Neznámy'})</span>
                        <span class="task-time">${statusText}</span>
                    </div>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

function updateScheduleView() {
    updateMonthlyCalendar();
    updateWeeklyDetail();
}

function updateMonthlyCalendar() {
    const today = new Date();
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();

    // Update month title
    const monthNames = ['Január', 'Február', 'Marec', 'Apríl', 'Máj', 'Jún',
                       'Júl', 'August', 'September', 'Október', 'November', 'December'];
    const monthTitle = document.getElementById('currentMonthTitle');
    if (monthTitle) {
        monthTitle.textContent = `${monthNames[currentMonth]} ${currentYear}`;
    }

    // Generate calendar days
    const firstDay = new Date(currentYear, currentMonth, 1);
    const lastDay = new Date(currentYear, currentMonth + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay() + 1); // Start from Monday

    const calendarGrid = document.getElementById('calendarDaysGrid');
    if (!calendarGrid) return;

    let html = '';
    const currentDate = new Date(startDate);

    for (let week = 0; week < 6; week++) {
        for (let day = 0; day < 7; day++) {
            const isCurrentMonth = currentDate.getMonth() === currentMonth;
            const isToday = currentDate.toDateString() === today.toDateString();
            const dayTasks = cleaningData.tasks.filter(t =>
                new Date(t.date).toDateString() === currentDate.toDateString()
            );

            const dayClass = `calendar-day ${isToday ? 'today' : ''} ${!isCurrentMonth ? 'other-month' : ''}`;

            html += `
                <div class="${dayClass}">
                    <div class="calendar-day-number">${currentDate.getDate()}</div>
                    ${dayTasks.length > 0 ? `<div class="calendar-task-count">${dayTasks.length}📋</div>` : ''}
                </div>
            `;

            currentDate.setDate(currentDate.getDate() + 1);
        }
    }

    calendarGrid.innerHTML = html;
}

function updateWeeklyDetail() {
    // This would show detailed weekly schedule
    // Implementation similar to the HTML structure shown in the design
}

function updateCustomersView() {
    const container = document.querySelector('.customers-list');
    if (!container) return;

    const html = cleaningData.customers.map(customer => {
        const paidStatus = customer.paid ?
            '<span class="paid">✅ Zaplatené</span>' :
            '<span class="unpaid">❌ Nezaplatené</span>';

        return `
            <div class="customer-card">
                <div class="customer-info">
                    <div class="customer-name">👤 ${customer.name}</div>
                    <div class="customer-contact">
                        <span>📞 ${customer.phone}</span>
                        <span>📧 ${customer.email}</span>
                    </div>
                    <div class="customer-details">
                        <span>🏛️ ${customer.graveNumber}</span>
                        <span>💰 ${customer.monthlyFee}€/mes</span>
                        ${paidStatus}
                    </div>
                    <div class="customer-schedule">
                        <span>📅 Posledné: ${customer.lastCleaning}</span>
                        <span>📅 Ďalšie: ${customer.nextCleaning}</span>
                    </div>
                </div>
                <div class="customer-actions">
                    <button class="btn btn-sm btn-secondary" onclick="viewCustomerDetail('${customer.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-info" onclick="callCustomer('${customer.phone}')">
                        <i class="fas fa-phone"></i>
                    </button>
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = html;
}

function updateGravesView() {
    const container = document.querySelector('.graves-list');
    if (!container) return;

    // Group graves by cemetery
    const gravesByCemetery = {};
    cleaningData.customers.forEach(customer => {
        if (!gravesByCemetery[customer.cemetery]) {
            gravesByCemetery[customer.cemetery] = [];
        }
        gravesByCemetery[customer.cemetery].push(customer);
    });

    let html = '';
    Object.keys(gravesByCemetery).forEach(cemetery => {
        const cemeteryName = CEMETERIES[cemetery] || cemetery;
        const graves = gravesByCemetery[cemetery];

        html += `
            <div class="cemetery-section">
                <h3><i class="fas fa-monument"></i> ${cemeteryName}</h3>
                <div class="graves-group">
        `;

        graves.forEach(customer => {
            // Find latest task for this grave
            const latestTask = cleaningData.tasks
                .filter(t => t.customerId === customer.id)
                .sort((a, b) => new Date(b.date) - new Date(a.date))[0];

            const statusIcon = latestTask ? getStatusIcon(latestTask.status) : '📅';
            const statusClass = latestTask ? getStatusClass(latestTask.status) : 'scheduled';
            const statusText = latestTask ?
                `${new Date(latestTask.date).toLocaleDateString('sk-SK')} ${getStatusText(latestTask.status)}` :
                'Naplánované';

            html += `
                <div class="grave-item ${statusClass}">
                    <div class="grave-info">
                        <span class="grave-number">${customer.graveNumber}</span>
                        <span class="grave-customer">(${customer.name})</span>
                        <span class="grave-status">${statusIcon} ${statusText}</span>
                    </div>
                    <div class="grave-actions">
                        <button class="btn btn-sm btn-secondary" onclick="viewGraveDetail('${customer.graveNumber}')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

function updatePaymentsView() {
    const container = document.querySelector('.payments-list');
    if (!container) return;

    const html = cleaningData.payments.map(payment => {
        const customer = cleaningData.customers.find(c => c.id === payment.customerId);
        const statusIcon = payment.paid ? '✅' : '❌';
        const statusClass = payment.paid ? 'paid' : 'overdue';
        const dateText = payment.paid ?
            `(${new Date(payment.date).toLocaleDateString('sk-SK')})` :
            `(po termíne ${payment.overdueDays || 0} dní)`;

        const actions = payment.paid ? `
            <button class="btn btn-sm btn-secondary" onclick="viewPaymentDetail('${payment.id}')">
                <i class="fas fa-eye"></i>
            </button>
        ` : `
            <button class="btn btn-sm btn-warning" onclick="sendReminder('${payment.id}')">
                <i class="fas fa-envelope"></i>
            </button>
            <button class="btn btn-sm btn-info" onclick="callCustomer('${customer ? customer.phone : ''}')">
                <i class="fas fa-phone"></i>
            </button>
            <button class="btn btn-sm btn-success" onclick="markAsPaid('${payment.id}')">
                <i class="fas fa-credit-card"></i>
            </button>
        `;

        return `
            <div class="payment-item ${statusClass}">
                <div class="payment-info">
                    <span class="payment-status">${statusIcon}</span>
                    <span class="payment-customer">${customer ? customer.name : 'Neznámy'}</span>
                    <span class="payment-amount">${payment.amount}€</span>
                    <span class="payment-date">${dateText}</span>
                </div>
                <div class="payment-actions">
                    ${actions}
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = html;
}

// Action functions
function viewCustomerDetail(customerId) {
    console.log('View customer detail:', customerId);
    // Implementation for customer detail modal
}

function callCustomer(phone) {
    if (phone) {
        window.open(`tel:${phone}`);
    }
}

function viewGraveDetail(graveNumber) {
    console.log('View grave detail:', graveNumber);
    // Implementation for grave detail modal
}

function sendReminder(paymentId) {
    console.log('Send reminder for payment:', paymentId);
    // Implementation for sending payment reminder
}

function markAsPaid(paymentId) {
    const payment = cleaningData.payments.find(p => p.id === paymentId);
    if (payment) {
        payment.paid = true;
        payment.paidDate = new Date().toISOString().split('T')[0];
        saveCleaningData();
        updatePaymentsView();
        updateCleaningDashboard();
        showNotification('Platba označená ako uhradená', 'success');
    }
}

function showNotification(message, type = 'info') {
    // Simple notification system
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
        color: white;
        border-radius: 8px;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Export functions for global access
window.cleaningSystem = {
    switchCleaningView,
    updateCleaningDashboard,
    updateTodayTasks,
    updateScheduleView,
    updateCustomersView,
    updateGravesView,
    updatePaymentsView,
    loadSampleCleaningData,
    cleaningData,
    viewCustomerDetail,
    callCustomer,
    viewGraveDetail,
    sendReminder,
    markAsPaid
};
